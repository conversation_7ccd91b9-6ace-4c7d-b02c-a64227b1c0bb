using System.Diagnostics;
using System.Reflection;
using System.Runtime.InteropServices;

namespace PreviewFramework.DotNetTool;

public static class Program
{
    public static async Task<int> Main(string[] args)
    {
        try
        {
            // Get the directory where this tool is installed
            string? toolDirectory = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
            if (string.IsNullOrEmpty(toolDirectory))
            {
                Console.Error.WriteLine("Error: Could not determine tool installation directory.");
                return 1;
            }

            // Look for the DevToolsApp executable in the tools/devtoolsapp subdirectory
            // The CLI tool is installed in tools/net9.0/any, so we need to go up to tools and then to devtoolsapp
            string devToolsAppDirectory = Path.Combine(toolDirectory, "..", "..", "devtoolsapp");
            string? devToolsAppExecutable = GetDevToolsAppExecutablePath(devToolsAppDirectory);

            if (string.IsNullOrEmpty(devToolsAppExecutable) || !File.Exists(devToolsAppExecutable))
            {
                Console.Error.WriteLine($"Error: PreviewFramework.DevToolsApp executable not found.");
                Console.Error.WriteLine($"Expected location: {devToolsAppDirectory}");
                return 1;
            }

            Console.WriteLine($"Launching PreviewFramework DevToolsApp from: {devToolsAppExecutable}");

            // Launch the DevToolsApp application
            ProcessStartInfo startInfo;

            if (devToolsAppExecutable.EndsWith(".dll"))
            {
                // Launch using dotnet
                startInfo = new ProcessStartInfo
                {
                    FileName = "dotnet",
                    Arguments = $"\"{devToolsAppExecutable}\" {string.Join(" ", args)}",
                    UseShellExecute = true,
                    CreateNoWindow = false
                };
            }
            else
            {
                // Launch executable directly
                startInfo = new ProcessStartInfo
                {
                    FileName = devToolsAppExecutable,
                    Arguments = string.Join(" ", args),
                    UseShellExecute = true,
                    CreateNoWindow = false
                };
            }

            using var process = Process.Start(startInfo);
            if (process == null)
            {
                Console.Error.WriteLine("Error: Failed to start PreviewFramework.DevToolsApp.");
                return 1;
            }

            // Wait for the process to exit
            await process.WaitForExitAsync();
            return process.ExitCode;
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Error launching PreviewFramework.DevToolsApp: {ex.Message}");
            return 1;
        }
    }

    private static string? GetDevToolsAppExecutablePath(string devToolsAppDirectory)
    {
        if (!Directory.Exists(devToolsAppDirectory))
        {
            return null;
        }

        // Look for the executable based on the platform
        if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
        {
            string exePath = Path.Combine(devToolsAppDirectory, "PreviewFramework.DevToolsApp.exe");
            if (File.Exists(exePath))
                return exePath;
        }

        // Fallback to the .dll file (can be executed with dotnet)
        string dllPath = Path.Combine(devToolsAppDirectory, "PreviewFramework.DevToolsApp.dll");
        if (File.Exists(dllPath))
        {
            return dllPath;
        }

        return null;
    }
}
